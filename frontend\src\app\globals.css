@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for the Task Management Application */

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-secondary-50 text-secondary-900 antialiased;
  }
  
  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }
}

@layer components {
  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }
  
  .btn-success {
    @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }
  
  .btn-warning {
    @apply bg-warning-600 hover:bg-warning-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2;
  }
  
  .btn-danger {
    @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2;
  }
  
  .btn-outline {
    @apply border border-secondary-300 hover:bg-secondary-50 text-secondary-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200 bg-secondary-50 rounded-b-xl;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full rounded-lg border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-2;
  }
  
  .form-error {
    @apply text-danger-600 text-sm mt-1;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-todo {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .badge-in-progress {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-completed {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-low {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .badge-medium {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-high {
    @apply bg-danger-100 text-danger-800;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-secondary-200 border-t-primary-600;
  }
  
  /* Animations */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
  
  .slide-down {
    @apply animate-slide-down;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Focus visible utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
  }
}
