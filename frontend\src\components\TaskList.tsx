'use client'

import { useState } from 'react'
import { Edit2, Trash2, Calendar, Clock, AlertCircle, CheckCircle2 } from 'lucide-react'
import { Task } from '@/types'
import { taskApi, getErrorMessage } from '@/lib/api'

interface TaskListProps {
  tasks: Task[]
  onEdit: (task: Task) => void
  onDelete: (taskId: string) => void
  onUpdate: () => void
}

export default function TaskList({ tasks, onEdit, onDelete, onUpdate }: TaskListProps) {
  const [updatingTasks, setUpdatingTasks] = useState<Set<string>>(new Set())

  const handleStatusChange = async (task: Task, newStatus: Task['status']) => {
    if (updatingTasks.has(task.id)) return

    setUpdatingTasks(prev => new Set(prev).add(task.id))

    try {
      await taskApi.update(task.id, { status: newStatus })
      onUpdate()
    } catch (error) {
      console.error('Error updating task status:', error)
      // You might want to show a toast notification here
    } finally {
      setUpdatingTasks(prev => {
        const newSet = new Set(prev)
        newSet.delete(task.id)
        return newSet
      })
    }
  }

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-danger-600 bg-danger-50 border-danger-200'
      case 'medium':
        return 'text-warning-600 bg-warning-50 border-warning-200'
      case 'low':
        return 'text-secondary-600 bg-secondary-50 border-secondary-200'
      default:
        return 'text-secondary-600 bg-secondary-50 border-secondary-200'
    }
  }

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'text-success-600 bg-success-50 border-success-200'
      case 'in-progress':
        return 'text-warning-600 bg-warning-50 border-warning-200'
      case 'todo':
        return 'text-secondary-600 bg-secondary-50 border-secondary-200'
      default:
        return 'text-secondary-600 bg-secondary-50 border-secondary-200'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const isOverdue = (dueDate: string | null) => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString()
  }

  const isDueToday = (dueDate: string | null) => {
    if (!dueDate) return false
    return new Date(dueDate).toDateString() === new Date().toDateString()
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <CheckCircle2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No tasks found</h3>
        <p className="text-secondary-600">
          Create your first task to get started with managing your work.
        </p>
      </div>
    )
  }

  return (
    <div className="divide-y divide-secondary-200">
      {tasks.map((task) => (
        <div
          key={task.id}
          className="p-6 hover:bg-secondary-50 transition-colors duration-200"
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {/* Task Header */}
              <div className="flex items-center space-x-3 mb-2">
                <button
                  onClick={() => handleStatusChange(
                    task,
                    task.status === 'completed' ? 'todo' : 'completed'
                  )}
                  disabled={updatingTasks.has(task.id)}
                  className={`flex-shrink-0 w-5 h-5 rounded-full border-2 transition-colors duration-200 ${
                    task.status === 'completed'
                      ? 'bg-success-600 border-success-600'
                      : 'border-secondary-300 hover:border-success-600'
                  } ${updatingTasks.has(task.id) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {task.status === 'completed' && (
                    <CheckCircle2 className="w-3 h-3 text-white m-0.5" />
                  )}
                </button>

                <h3 className={`text-lg font-medium ${
                  task.status === 'completed' 
                    ? 'text-secondary-500 line-through' 
                    : 'text-secondary-900'
                }`}>
                  {task.title}
                </h3>

                {/* Priority Badge */}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                  {task.priority}
                </span>

                {/* Status Badge */}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                  {task.status.replace('-', ' ')}
                </span>
              </div>

              {/* Task Description */}
              {task.description && (
                <p className={`text-sm mb-3 ${
                  task.status === 'completed' 
                    ? 'text-secondary-400' 
                    : 'text-secondary-600'
                }`}>
                  {task.description}
                </p>
              )}

              {/* Task Metadata */}
              <div className="flex items-center space-x-4 text-sm text-secondary-500">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>Created {formatDate(task.createdAt)}</span>
                </div>

                {task.dueDate && (
                  <div className={`flex items-center space-x-1 ${
                    isOverdue(task.dueDate) 
                      ? 'text-danger-600' 
                      : isDueToday(task.dueDate)
                      ? 'text-warning-600'
                      : 'text-secondary-500'
                  }`}>
                    <Calendar className="w-4 h-4" />
                    <span>
                      Due {formatDate(task.dueDate)}
                      {isOverdue(task.dueDate) && ' (Overdue)'}
                      {isDueToday(task.dueDate) && ' (Today)'}
                    </span>
                    {isOverdue(task.dueDate) && (
                      <AlertCircle className="w-4 h-4" />
                    )}
                  </div>
                )}

                <div className="flex items-center space-x-1">
                  <span>by {task.createdBy}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={() => onEdit(task)}
                className="p-2 text-secondary-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                title="Edit task"
              >
                <Edit2 className="w-4 h-4" />
              </button>

              <button
                onClick={() => onDelete(task.id)}
                className="p-2 text-secondary-400 hover:text-danger-600 hover:bg-danger-50 rounded-lg transition-colors duration-200"
                title="Delete task"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Quick Status Update Buttons */}
          {task.status !== 'completed' && (
            <div className="mt-4 flex items-center space-x-2">
              <span className="text-sm text-secondary-600">Quick update:</span>
              {task.status === 'todo' && (
                <button
                  onClick={() => handleStatusChange(task, 'in-progress')}
                  disabled={updatingTasks.has(task.id)}
                  className="text-xs px-2 py-1 bg-warning-100 text-warning-700 rounded hover:bg-warning-200 transition-colors duration-200 disabled:opacity-50"
                >
                  Start Working
                </button>
              )}
              {task.status === 'in-progress' && (
                <button
                  onClick={() => handleStatusChange(task, 'completed')}
                  disabled={updatingTasks.has(task.id)}
                  className="text-xs px-2 py-1 bg-success-100 text-success-700 rounded hover:bg-success-200 transition-colors duration-200 disabled:opacity-50"
                >
                  Mark Complete
                </button>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
